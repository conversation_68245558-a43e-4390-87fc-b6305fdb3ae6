const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 确保聊天数据目录存在
const CHATS_DIR = path.join(__dirname, 'chats');
fs.ensureDirSync(CHATS_DIR);

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({ success: true, message: '服务器正常运行' });
});

// 创建新的聊天会话
app.post('/api/chat/new', async (req, res) => {
  try {
    const chatId = uuidv4();
    const chatDir = path.join(CHATS_DIR, chatId);
    
    // 创建聊天目录
    await fs.ensureDir(chatDir);
    
    // 创建初始元数据
    const metadata = {
      chatId,
      createdAt: new Date().toISOString(),
      title: '新对话',
      totalRounds: 0,
      apiProvider: 'deepseek'
    };
    
    await fs.writeJson(path.join(chatDir, 'metadata.json'), metadata, { spaces: 2 });
    
    res.json({ success: true, chatId, metadata });
  } catch (error) {
    console.error('创建新聊天失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取所有聊天列表
app.get('/api/chats', async (req, res) => {
  try {
    const chatDirs = await fs.readdir(CHATS_DIR);
    const chats = [];
    
    for (const chatDir of chatDirs) {
      const chatPath = path.join(CHATS_DIR, chatDir);
      const metadataPath = path.join(chatPath, 'metadata.json');
      
      if (await fs.pathExists(metadataPath)) {
        const metadata = await fs.readJson(metadataPath);
        chats.push(metadata);
      }
    }
    
    // 按创建时间排序
    chats.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    res.json({ success: true, chats });
  } catch (error) {
    console.error('获取聊天列表失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({ success: false, error: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ success: false, error: '接口不存在' });
});

app.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 简化服务器运行在 http://localhost:${PORT}`);
  console.log(`📁 聊天数据目录: ${CHATS_DIR}`);
});
