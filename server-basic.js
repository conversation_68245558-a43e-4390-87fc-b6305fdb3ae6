const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 确保聊天数据目录存在
const CHATS_DIR = path.join(__dirname, 'chats');
fs.ensureDirSync(CHATS_DIR);

// 创建新的聊天会话
app.post('/api/chat/new', async (req, res) => {
  try {
    const chatId = uuidv4();
    const chatDir = path.join(CHATS_DIR, chatId);
    
    // 创建聊天目录
    await fs.ensureDir(chatDir);
    
    // 创建初始元数据
    const metadata = {
      chatId,
      createdAt: new Date().toISOString(),
      title: '新对话',
      totalRounds: 0,
      apiProvider: 'deepseek'
    };
    
    await fs.writeJson(path.join(chatDir, 'metadata.json'), metadata, { spaces: 2 });
    
    res.json({ success: true, chatId, metadata });
  } catch (error) {
    console.error('创建新聊天失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取所有聊天列表
app.get('/api/chats', async (req, res) => {
  try {
    const chatDirs = await fs.readdir(CHATS_DIR);
    const chats = [];
    
    for (const chatDir of chatDirs) {
      const chatPath = path.join(CHATS_DIR, chatDir);
      const metadataPath = path.join(chatPath, 'metadata.json');
      
      if (await fs.pathExists(metadataPath)) {
        const metadata = await fs.readJson(metadataPath);
        chats.push(metadata);
      }
    }
    
    // 按创建时间排序
    chats.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    res.json({ success: true, chats });
  } catch (error) {
    console.error('获取聊天列表失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 发送消息（模拟AI回复）
app.post('/api/chat/:chatId/message', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { message, apiProvider = 'deepseek' } = req.body;
    
    const chatDir = path.join(CHATS_DIR, chatId);
    const metadataPath = path.join(chatDir, 'metadata.json');
    
    if (!await fs.pathExists(metadataPath)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }
    
    const metadata = await fs.readJson(metadataPath);
    const roundNumber = metadata.totalRounds + 1;
    
    // 获取历史上下文
    const context = await getContextHistory(chatDir, roundNumber - 1);
    
    // 模拟AI回复
    const aiResponse = `这是来自${apiProvider}的模拟回复：我收到了您的消息"${message}"。这是第${roundNumber}轮对话。`;
    
    // 保存这一轮的对话JSON
    const roundData = {
      roundNumber,
      timestamp: new Date().toISOString(),
      userMessage: message,
      aiResponse: aiResponse,
      context: [...context, 
        { role: 'user', content: message },
        { role: 'assistant', content: aiResponse }
      ],
      apiProvider
    };
    
    const roundFile = path.join(chatDir, `round_${roundNumber.toString().padStart(3, '0')}.json`);
    await fs.writeJson(roundFile, roundData, { spaces: 2 });
    
    // 更新元数据
    metadata.totalRounds = roundNumber;
    metadata.lastUpdated = new Date().toISOString();
    metadata.apiProvider = apiProvider;
    
    // 如果是第一轮，用用户消息的前30个字符作为标题
    if (roundNumber === 1) {
      metadata.title = message.substring(0, 30) + (message.length > 30 ? '...' : '');
    }
    
    await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    
    res.json({ 
      success: true, 
      response: aiResponse, 
      roundNumber,
      metadata 
    });
    
  } catch (error) {
    console.error('发送消息失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取聊天历史
app.get('/api/chat/:chatId/history', async (req, res) => {
  try {
    const { chatId } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);
    
    if (!await fs.pathExists(chatDir)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }
    
    const files = await fs.readdir(chatDir);
    const roundFiles = files.filter(f => f.startsWith('round_') && f.endsWith('.json'));
    
    const history = [];
    for (const file of roundFiles.sort()) {
      const roundData = await fs.readJson(path.join(chatDir, file));
      history.push(roundData);
    }
    
    res.json({ success: true, history });
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取上下文历史（用于AI调用）
async function getContextHistory(chatDir, maxRounds = 10) {
  try {
    const files = await fs.readdir(chatDir);
    const roundFiles = files
      .filter(f => f.startsWith('round_') && f.endsWith('.json'))
      .sort()
      .slice(-maxRounds); // 只取最近的几轮
    
    const context = [];
    for (const file of roundFiles) {
      const roundData = await fs.readJson(path.join(chatDir, file));
      context.push(
        { role: 'user', content: roundData.userMessage },
        { role: 'assistant', content: roundData.aiResponse }
      );
    }
    
    return context;
  } catch (error) {
    console.error('获取上下文历史失败:', error);
    return [];
  }
}

// 获取特定轮次的JSON数据
app.get('/api/chat/:chatId/round/:roundNumber', async (req, res) => {
  try {
    const { chatId, roundNumber } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);
    const roundFile = path.join(chatDir, `round_${roundNumber.padStart(3, '0')}.json`);
    
    if (!await fs.pathExists(roundFile)) {
      return res.status(404).json({ success: false, error: '轮次不存在' });
    }
    
    const roundData = await fs.readJson(roundFile);
    res.json({ success: true, data: roundData });
  } catch (error) {
    console.error('获取轮次数据失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 更新特定轮次的JSON数据
app.put('/api/chat/:chatId/round/:roundNumber', async (req, res) => {
  try {
    const { chatId, roundNumber } = req.params;
    const { data } = req.body;
    
    const chatDir = path.join(CHATS_DIR, chatId);
    const roundFile = path.join(chatDir, `round_${roundNumber.padStart(3, '0')}.json`);
    
    if (!await fs.pathExists(roundFile)) {
      return res.status(404).json({ success: false, error: '轮次不存在' });
    }
    
    // 添加更新时间戳
    data.lastModified = new Date().toISOString();
    
    await fs.writeJson(roundFile, data, { spaces: 2 });
    res.json({ success: true, message: '更新成功' });
  } catch (error) {
    console.error('更新轮次数据失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 删除特定轮次
app.delete('/api/chat/:chatId/round/:roundNumber', async (req, res) => {
  try {
    const { chatId, roundNumber } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);
    const roundFile = path.join(chatDir, `round_${roundNumber.padStart(3, '0')}.json`);
    
    if (!await fs.pathExists(roundFile)) {
      return res.status(404).json({ success: false, error: '轮次不存在' });
    }
    
    await fs.remove(roundFile);
    
    // 更新元数据中的总轮数
    const metadataPath = path.join(chatDir, 'metadata.json');
    if (await fs.pathExists(metadataPath)) {
      const metadata = await fs.readJson(metadataPath);
      const files = await fs.readdir(chatDir);
      const remainingRounds = files.filter(f => f.startsWith('round_') && f.endsWith('.json')).length;
      metadata.totalRounds = remainingRounds;
      metadata.lastUpdated = new Date().toISOString();
      await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    }
    
    res.json({ success: true, message: '删除成功' });
  } catch (error) {
    console.error('删除轮次失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 删除整个聊天
app.delete('/api/chat/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);
    
    if (!await fs.pathExists(chatDir)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }
    
    await fs.remove(chatDir);
    res.json({ success: true, message: '聊天删除成功' });
  } catch (error) {
    console.error('删除聊天失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 批量删除轮次
app.post('/api/chat/:chatId/batch-delete', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { roundNumbers } = req.body;
    
    const chatDir = path.join(CHATS_DIR, chatId);
    const deletedRounds = [];
    
    for (const roundNumber of roundNumbers) {
      const roundFile = path.join(chatDir, `round_${roundNumber.toString().padStart(3, '0')}.json`);
      if (await fs.pathExists(roundFile)) {
        await fs.remove(roundFile);
        deletedRounds.push(roundNumber);
      }
    }
    
    // 更新元数据
    const metadataPath = path.join(chatDir, 'metadata.json');
    if (await fs.pathExists(metadataPath)) {
      const metadata = await fs.readJson(metadataPath);
      const files = await fs.readdir(chatDir);
      const remainingRounds = files.filter(f => f.startsWith('round_') && f.endsWith('.json')).length;
      metadata.totalRounds = remainingRounds;
      metadata.lastUpdated = new Date().toISOString();
      await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    }
    
    res.json({ success: true, deletedRounds, message: `成功删除 ${deletedRounds.length} 轮对话` });
  } catch (error) {
    console.error('批量删除失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 更新聊天标题
app.put('/api/chat/:chatId/title', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { title } = req.body;
    
    const chatDir = path.join(CHATS_DIR, chatId);
    const metadataPath = path.join(chatDir, 'metadata.json');
    
    if (!await fs.pathExists(metadataPath)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }
    
    const metadata = await fs.readJson(metadataPath);
    metadata.title = title;
    metadata.lastUpdated = new Date().toISOString();
    
    await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    res.json({ success: true, message: '标题更新成功' });
  } catch (error) {
    console.error('更新标题失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({ success: false, error: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ success: false, error: '接口不存在' });
});

app.listen(PORT, () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📁 聊天数据目录: ${CHATS_DIR}`);
  console.log(`💡 当前使用模拟AI回复，可以测试所有功能`);
});
