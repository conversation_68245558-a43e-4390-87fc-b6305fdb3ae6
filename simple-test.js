// 简单测试脚本
const axios = require('axios');

async function simpleTest() {
    try {
        console.log('测试服务器连接...');
        
        // 测试服务器是否响应
        const healthResponse = await axios.get('http://localhost:3000');
        console.log('✅ 服务器响应正常');
        
        // 创建新对话
        console.log('创建新对话...');
        const newChatResponse = await axios.post('http://localhost:3000/api/chat/new');
        console.log('✅ 新对话创建成功:', newChatResponse.data.chatId);
        
        const chatId = newChatResponse.data.chatId;
        
        // 发送简单消息
        console.log('发送消息...');
        const messageResponse = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
            message: '你好',
            apiProvider: 'deepseek'
        }, {
            timeout: 60000 // 60秒超时
        });
        
        console.log('✅ 消息发送成功');
        console.log('AI回复:', messageResponse.data.response.substring(0, 100) + '...');
        
    } catch (error) {
        console.error('❌ 测试失败:');
        console.error('错误类型:', error.code);
        console.error('错误信息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

simpleTest();
