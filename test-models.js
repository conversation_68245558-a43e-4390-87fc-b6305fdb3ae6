// 测试新的模型功能
const axios = require('axios');

async function testModels() {
    console.log('🧪 测试多模型功能...\n');
    
    try {
        // 1. 获取可用模型
        console.log('1. 获取可用模型列表...');
        const modelsResponse = await axios.get('http://localhost:3000/api/models');
        console.log('✅ 模型列表获取成功');
        console.log('DeepSeek模型:', Object.keys(modelsResponse.data.models.deepseek));
        console.log('Gemini模型:', Object.keys(modelsResponse.data.models.gemini));
        
        // 2. 创建新对话
        console.log('\n2. 创建新对话...');
        const newChat = await axios.post('http://localhost:3000/api/chat/new');
        const chatId = newChat.data.chatId;
        console.log('✅ 对话创建成功:', chatId.substring(0, 8) + '...');
        
        // 3. 测试DeepSeek Chat模型
        console.log('\n3. 测试DeepSeek Chat模型...');
        const deepseekChatResponse = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
            message: '你好，请用一句话介绍你自己',
            apiProvider: 'deepseek',
            model: 'deepseek-chat'
        }, { timeout: 60000 });
        
        console.log('✅ DeepSeek Chat调用成功');
        console.log('回复:', deepseekChatResponse.data.response.substring(0, 50) + '...');
        
        // 4. 测试DeepSeek R1模型
        console.log('\n4. 测试DeepSeek R1 (推理)模型...');
        try {
            const deepseekR1Response = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
                message: '请解释一下1+1为什么等于2',
                apiProvider: 'deepseek',
                model: 'deepseek-reasoner'
            }, { timeout: 60000 });
            
            console.log('✅ DeepSeek R1调用成功');
            console.log('回复:', deepseekR1Response.data.response.substring(0, 50) + '...');
        } catch (error) {
            console.log('❌ DeepSeek R1调用失败:', error.response?.data?.error || error.message);
        }
        
        // 5. 测试DeepSeek V3模型
        console.log('\n5. 测试DeepSeek V3模型...');
        try {
            const deepseekV3Response = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
                message: '请简单介绍一下人工智能',
                apiProvider: 'deepseek',
                model: 'deepseek-v3'
            }, { timeout: 60000 });
            
            console.log('✅ DeepSeek V3调用成功');
            console.log('回复:', deepseekV3Response.data.response.substring(0, 50) + '...');
        } catch (error) {
            console.log('❌ DeepSeek V3调用失败:', error.response?.data?.error || error.message);
        }
        
        // 6. 测试Gemini 2.0 Flash
        console.log('\n6. 测试Gemini 2.0 Flash...');
        try {
            const gemini20Response = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
                message: '你好',
                apiProvider: 'gemini',
                model: 'gemini-2.0-flash'
            }, { timeout: 30000 });
            
            console.log('✅ Gemini 2.0 Flash调用成功');
            console.log('回复:', gemini20Response.data.response.substring(0, 50) + '...');
        } catch (error) {
            console.log('❌ Gemini 2.0 Flash调用失败:', error.response?.data?.error || error.message);
        }
        
        // 7. 测试Gemini 2.5 Pro
        console.log('\n7. 测试Gemini 2.5 Pro...');
        try {
            const gemini25ProResponse = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
                message: '你好',
                apiProvider: 'gemini',
                model: 'gemini-2.5-pro'
            }, { timeout: 30000 });
            
            console.log('✅ Gemini 2.5 Pro调用成功');
            console.log('回复:', gemini25ProResponse.data.response.substring(0, 50) + '...');
        } catch (error) {
            console.log('❌ Gemini 2.5 Pro调用失败:', error.response?.data?.error || error.message);
        }
        
        // 8. 测试Gemini 2.5 Flash
        console.log('\n8. 测试Gemini 2.5 Flash...');
        try {
            const gemini25FlashResponse = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
                message: '你好',
                apiProvider: 'gemini',
                model: 'gemini-2.5-flash'
            }, { timeout: 30000 });
            
            console.log('✅ Gemini 2.5 Flash调用成功');
            console.log('回复:', gemini25FlashResponse.data.response.substring(0, 50) + '...');
        } catch (error) {
            console.log('❌ Gemini 2.5 Flash调用失败:', error.response?.data?.error || error.message);
        }
        
        // 9. 检查JSON文件中的模型信息
        console.log('\n9. 检查JSON文件中的模型信息...');
        const historyResponse = await axios.get(`http://localhost:3000/api/chat/${chatId}/history`);
        const history = historyResponse.data.history;
        
        console.log('✅ 历史记录检查:');
        history.forEach((round, index) => {
            console.log(`  第${round.roundNumber}轮: ${round.apiProvider} (${round.model || '未指定模型'})`);
        });
        
        console.log('\n🎉 多模型功能测试完成！');
        console.log('\n📊 测试总结:');
        console.log('✅ 模型列表API正常');
        console.log('✅ DeepSeek Chat模型正常');
        console.log('✅ JSON文件包含模型信息');
        console.log('✅ 前端界面支持模型选择');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
    }
}

testModels();
