// 直接测试Gemini API
const axios = require('axios');

const GEMINI_CONFIG = {
  url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
  keys: [
    'AIzaSyA_7e5i1X0Qwqt64GM0YxAu4AuUBXlM2gU',
    'AIzaSyDL-n-QZrMGnBdZPuuX7XMJ5htg-jEA8Bc'
  ]
};

async function testGeminiDirect() {
  console.log('🧪 直接测试Gemini API...\n');
  
  const contents = [
    {
      parts: [{ text: '你好，请简单介绍一下你自己，用中文回复' }],
      role: 'user'
    }
  ];
  
  for (let i = 0; i < GEMINI_CONFIG.keys.length; i++) {
    const apiKey = GEMINI_CONFIG.keys[i];
    console.log(`测试API密钥 ${i + 1}/${GEMINI_CONFIG.keys.length}: ${apiKey.substring(0, 10)}...`);
    
    try {
      const response = await axios.post(GEMINI_CONFIG.url, {
        contents: contents,
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 4000
        }
      }, {
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': apiKey
        },
        timeout: 30000
      });
      
      console.log('✅ API密钥', i + 1, '调用成功！');
      console.log('回复内容:', response.data.candidates[0].content.parts[0].text.substring(0, 100) + '...');
      return;
      
    } catch (error) {
      console.error(`❌ API密钥 ${i + 1} 调用失败:`);
      console.error('错误类型:', error.code);
      console.error('状态码:', error.response?.status);
      console.error('错误信息:', error.response?.data);
      console.error('错误详情:', error.message);
      console.error('完整错误:', JSON.stringify(error, null, 2));
      console.log('');
    }
  }
  
  console.log('❌ 所有Gemini API密钥都调用失败');
}

testGeminiDirect();
