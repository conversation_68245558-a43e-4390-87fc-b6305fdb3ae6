// 快速功能测试
const axios = require('axios');

async function quickTest() {
    console.log('🚀 快速功能测试开始...\n');
    
    try {
        // 1. 测试服务器
        console.log('1. 测试服务器连接...');
        await axios.get('http://localhost:3000');
        console.log('✅ 服务器连接正常');
        
        // 2. 创建对话
        console.log('\n2. 创建新对话...');
        const newChat = await axios.post('http://localhost:3000/api/chat/new');
        const chatId = newChat.data.chatId;
        console.log('✅ 对话创建成功:', chatId.substring(0, 8) + '...');
        
        // 3. 发送消息
        console.log('\n3. 发送消息到DeepSeek...');
        const msgResponse = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
            message: '你好，请用一句话介绍你自己',
            apiProvider: 'deepseek'
        }, { timeout: 60000 });
        
        console.log('✅ 消息发送成功');
        console.log('AI回复:', msgResponse.data.response.substring(0, 50) + '...');
        
        // 4. 测试上下文
        console.log('\n4. 测试上下文传递...');
        const contextResponse = await axios.post(`http://localhost:3000/api/chat/${chatId}/message`, {
            message: '我刚才问了什么？',
            apiProvider: 'deepseek'
        }, { timeout: 60000 });
        
        console.log('✅ 上下文测试成功');
        console.log('AI回复:', contextResponse.data.response.substring(0, 50) + '...');
        
        // 5. 获取历史
        console.log('\n5. 获取聊天历史...');
        const history = await axios.get(`http://localhost:3000/api/chat/${chatId}/history`);
        console.log('✅ 历史获取成功，共', history.data.history.length, '轮对话');
        
        // 6. 获取JSON数据
        console.log('\n6. 获取JSON数据...');
        const jsonData = await axios.get(`http://localhost:3000/api/chat/${chatId}/round/1`);
        console.log('✅ JSON数据获取成功');
        console.log('上下文项目数:', jsonData.data.data.context.length);
        
        // 7. 编辑JSON
        console.log('\n7. 测试JSON编辑...');
        const data = jsonData.data.data;
        data.testEdit = '测试编辑功能';
        
        const editResponse = await axios.put(`http://localhost:3000/api/chat/${chatId}/round/1`, { data });
        console.log('✅ JSON编辑成功');
        
        // 8. 获取对话列表
        console.log('\n8. 获取对话列表...');
        const chatList = await axios.get('http://localhost:3000/api/chats');
        console.log('✅ 对话列表获取成功，共', chatList.data.chats.length, '个对话');
        
        console.log('\n🎉 所有核心功能测试通过！');
        console.log('\n📋 功能状态总结:');
        console.log('✅ 服务器运行正常');
        console.log('✅ 对话创建和管理');
        console.log('✅ DeepSeek API调用');
        console.log('✅ 上下文保持和传递');
        console.log('✅ JSON文件自动生成');
        console.log('✅ JSON数据编辑');
        console.log('✅ 聊天历史管理');
        console.log('\n🚀 系统完全就绪，可以正常使用！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
        console.error('错误详情:', error.code);
    }
}

quickTest();
