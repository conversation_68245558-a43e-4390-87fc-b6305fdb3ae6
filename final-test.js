// 最终功能测试脚本
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api';

async function finalTest() {
    try {
        console.log('🎯 开始最终功能测试...\n');
        
        // 1. 创建新对话
        console.log('1. 创建新对话...');
        const newChatResponse = await axios.post(`${BASE_URL}/chat/new`);
        console.log('✅ 新对话创建成功');
        const chatId = newChatResponse.data.chatId;
        console.log('对话ID:', chatId);
        
        // 2. 发送第一条消息
        console.log('\n2. 发送第一条消息...');
        const message1Response = await axios.post(`${BASE_URL}/chat/${chatId}/message`, {
            message: '你好，我想了解一下人工智能的发展历史',
            apiProvider: 'deepseek'
        });
        console.log('✅ 第一条消息发送成功');
        console.log('AI回复长度:', message1Response.data.response.length, '字符');
        
        // 3. 发送第二条消息（测试上下文）
        console.log('\n3. 发送第二条消息（测试上下文）...');
        const message2Response = await axios.post(`${BASE_URL}/chat/${chatId}/message`, {
            message: '请详细说明一下你刚才提到的深度学习阶段',
            apiProvider: 'deepseek'
        });
        console.log('✅ 第二条消息发送成功');
        console.log('AI回复长度:', message2Response.data.response.length, '字符');
        
        // 4. 发送第三条消息
        console.log('\n4. 发送第三条消息...');
        const message3Response = await axios.post(`${BASE_URL}/chat/${chatId}/message`, {
            message: '现在AI技术的主要应用领域有哪些？',
            apiProvider: 'deepseek'
        });
        console.log('✅ 第三条消息发送成功');
        console.log('AI回复长度:', message3Response.data.response.length, '字符');
        
        // 5. 获取聊天历史
        console.log('\n5. 获取聊天历史...');
        const historyResponse = await axios.get(`${BASE_URL}/chat/${chatId}/history`);
        console.log('✅ 聊天历史获取成功');
        console.log('总轮数:', historyResponse.data.history.length);
        
        // 6. 检查JSON文件是否正确生成
        console.log('\n6. 检查JSON文件生成...');
        const chatDir = path.join(__dirname, 'chats', chatId);
        
        // 检查元数据文件
        const metadataPath = path.join(chatDir, 'metadata.json');
        if (fs.existsSync(metadataPath)) {
            const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
            console.log('✅ 元数据文件存在');
            console.log('标题:', metadata.title);
            console.log('总轮数:', metadata.totalRounds);
        } else {
            console.log('❌ 元数据文件不存在');
        }
        
        // 检查轮次文件
        for (let i = 1; i <= 3; i++) {
            const roundFile = path.join(chatDir, `round_${i.toString().padStart(3, '0')}.json`);
            if (fs.existsSync(roundFile)) {
                const roundData = JSON.parse(fs.readFileSync(roundFile, 'utf8'));
                console.log(`✅ 第${i}轮JSON文件存在`);
                console.log(`   - 用户消息长度: ${roundData.userMessage.length} 字符`);
                console.log(`   - AI回复长度: ${roundData.aiResponse.length} 字符`);
                console.log(`   - 上下文项目数: ${roundData.context.length} 个`);
            } else {
                console.log(`❌ 第${i}轮JSON文件不存在`);
            }
        }
        
        // 7. 测试JSON编辑功能
        console.log('\n7. 测试JSON编辑功能...');
        const round1Response = await axios.get(`${BASE_URL}/chat/${chatId}/round/1`);
        if (round1Response.data.success) {
            console.log('✅ 获取第1轮JSON数据成功');
            
            // 修改JSON数据
            const modifiedData = round1Response.data.data;
            modifiedData.testField = '这是测试修改';
            
            const updateResponse = await axios.put(`${BASE_URL}/chat/${chatId}/round/1`, {
                data: modifiedData
            });
            
            if (updateResponse.data.success) {
                console.log('✅ JSON数据修改成功');
            } else {
                console.log('❌ JSON数据修改失败');
            }
        }
        
        // 8. 测试上下文管理
        console.log('\n8. 测试上下文管理...');
        const round2Response = await axios.get(`${BASE_URL}/chat/${chatId}/round/2`);
        if (round2Response.data.success) {
            const contextLength = round2Response.data.data.context.length;
            console.log('✅ 第2轮上下文项目数:', contextLength);
            
            // 验证上下文包含历史对话
            if (contextLength >= 4) { // 至少包含前两轮的用户和AI消息
                console.log('✅ 上下文正确包含历史对话');
            } else {
                console.log('❌ 上下文可能缺少历史对话');
            }
        }
        
        // 9. 测试聊天列表
        console.log('\n9. 测试聊天列表...');
        const chatsResponse = await axios.get(`${BASE_URL}/chats`);
        if (chatsResponse.data.success) {
            console.log('✅ 聊天列表获取成功');
            console.log('聊天数量:', chatsResponse.data.chats.length);
            
            const currentChat = chatsResponse.data.chats.find(chat => chat.chatId === chatId);
            if (currentChat) {
                console.log('✅ 当前聊天在列表中找到');
                console.log('聊天标题:', currentChat.title);
            }
        }
        
        // 10. 测试标题更新
        console.log('\n10. 测试标题更新...');
        const titleResponse = await axios.put(`${BASE_URL}/chat/${chatId}/title`, {
            title: 'AI发展历史讨论'
        });
        if (titleResponse.data.success) {
            console.log('✅ 标题更新成功');
        }
        
        console.log('\n🎉 所有功能测试完成！');
        console.log('\n📋 测试总结:');
        console.log('- ✅ 对话创建和消息发送');
        console.log('- ✅ 上下文保持和传递');
        console.log('- ✅ JSON文件自动生成');
        console.log('- ✅ JSON数据编辑');
        console.log('- ✅ 聊天历史管理');
        console.log('- ✅ 标题更新');
        console.log('\n🚀 系统已完全就绪，可以正常使用！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
    }
}

// 运行测试
finalTest();
