// 全局变量
let currentChatId = null;
let currentRoundNumber = null;
let selectedRounds = new Set();
let chats = [];
let availableModels = {};
let currentModel = 'auto'; // 默认智能模式
let isFirstMessage = true;

// API基础URL
const API_BASE = '/api';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadChatList();
    loadAvailableModels();

    // 绑定回车键发送消息
    document.getElementById('messageInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 聚焦到输入框
    document.getElementById('messageInput').focus();
});

// 智能模式切换
function switchModel(model) {
    currentModel = model;
    const modelNames = {
        'auto': '🤖 智能模式',
        'deepseek-chat': '💬 对话模式',
        'deepseek-reasoner': '🧠 推理模式',
        'gemini-2.0-flash': '⚡ 快速模式'
    };
    document.getElementById('currentModel').textContent = modelNames[model] || '智能模式';
}

// 切换高级设置
function toggleAdvancedSettings() {
    const contextPanel = document.getElementById('contextPanel');
    const isVisible = contextPanel.style.display !== 'none';
    contextPanel.style.display = isVisible ? 'none' : 'block';
}

// 智能选择模型
function getSmartModel(message) {
    if (currentModel !== 'auto') {
        // 用户手动选择了模型
        if (currentModel.startsWith('deepseek')) {
            return { apiProvider: 'deepseek', selectedModel: currentModel };
        } else if (currentModel.startsWith('gemini')) {
            return { apiProvider: 'gemini', selectedModel: currentModel };
        }
    }

    // 智能模式：根据消息内容选择最适合的模型
    const lowerMessage = message.toLowerCase();

    // 编程相关 - 使用DeepSeek Chat
    if (lowerMessage.includes('代码') || lowerMessage.includes('编程') ||
        lowerMessage.includes('bug') || lowerMessage.includes('函数') ||
        lowerMessage.includes('算法') || lowerMessage.includes('debug')) {
        return { apiProvider: 'deepseek', selectedModel: 'deepseek-chat' };
    }

    // 复杂推理 - 使用DeepSeek Reasoner
    if (lowerMessage.includes('分析') || lowerMessage.includes('推理') ||
        lowerMessage.includes('逻辑') || lowerMessage.includes('证明') ||
        lowerMessage.includes('解释为什么') || lowerMessage.includes('原理')) {
        return { apiProvider: 'deepseek', selectedModel: 'deepseek-reasoner' };
    }

    // 快速问答 - 使用Gemini Flash
    return { apiProvider: 'gemini', selectedModel: 'gemini-2.0-flash' };
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// 显示/隐藏输入状态
function showTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'block';
}

function hideTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'none';
}

// 禁用/启用输入
function disableInput(disabled) {
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');

    messageInput.disabled = disabled;
    sendBtn.disabled = disabled;

    if (disabled) {
        sendBtn.innerHTML = '<i class="bi bi-hourglass-split"></i><span class="d-none d-sm-inline ms-1">发送中</span>';
    } else {
        sendBtn.innerHTML = '<i class="bi bi-send"></i><span class="d-none d-sm-inline ms-1">发送</span>';
    }
}

// 添加消息到聊天界面
function addMessageToChat(role, content) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;

    if (role === 'user') {
        messageDiv.innerHTML = `<div>${content}</div>`;
    } else {
        messageDiv.innerHTML = `<div>${formatAIResponse(content)}</div>`;
    }

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 格式化AI回复
function formatAIResponse(content) {
    // 简单的markdown格式化
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>');
}

// 获取相对时间
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return '刚刚';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes}分钟前`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours}小时前`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days}天前`;
    } else {
        return date.toLocaleDateString();
    }
}

// 移动端功能
function toggleMobileSidebar() {
    const sidebar = document.querySelector('.chat-sidebar');
    const overlay = document.querySelector('.mobile-overlay');

    sidebar.classList.toggle('show');
    overlay.style.display = sidebar.classList.contains('show') ? 'block' : 'none';
}

function toggleMobileSettings() {
    const contextPanel = document.getElementById('contextPanel');
    const overlay = document.querySelector('.mobile-overlay');

    contextPanel.classList.toggle('show');
    overlay.style.display = contextPanel.classList.contains('show') ? 'block' : 'none';
}

function closeMobileMenus() {
    const sidebar = document.querySelector('.chat-sidebar');
    const contextPanel = document.getElementById('contextPanel');
    const overlay = document.querySelector('.mobile-overlay');

    sidebar.classList.remove('show');
    contextPanel.classList.remove('show');
    overlay.style.display = 'none';
}

// 快速消息设置
function setQuickMessage(message) {
    const messageInput = document.getElementById('messageInput');
    messageInput.value = message;
    messageInput.focus();

    // 添加一个小动画效果
    messageInput.style.transform = 'scale(1.02)';
    setTimeout(() => {
        messageInput.style.transform = 'scale(1)';
    }, 200);
}

// 加载可用模型
async function loadAvailableModels() {
    try {
        const response = await fetch(`${API_BASE}/models`);
        const result = await response.json();

        if (result.success) {
            availableModels = result.models;
            updateModelOptions(); // 初始化模型选项
        }
    } catch (error) {
        console.error('加载模型列表失败:', error);
    }
}

// 更新模型选择选项
function updateModelOptions() {
    const apiProvider = document.getElementById('apiProvider').value;
    const modelSelect = document.getElementById('modelSelect');

    // 清空现有选项
    modelSelect.innerHTML = '<option value="">选择模型...</option>';

    if (availableModels[apiProvider]) {
        const models = availableModels[apiProvider];

        for (const [key, name] of Object.entries(models)) {
            const option = document.createElement('option');
            option.value = key;
            option.textContent = name;
            modelSelect.appendChild(option);
        }

        // 设置默认选择
        if (apiProvider === 'deepseek') {
            modelSelect.value = 'deepseek-chat';
        } else if (apiProvider === 'gemini') {
            modelSelect.value = 'gemini-2.0-flash';
        }
    }
}

// 创建新对话
async function createNewChat() {
    try {
        const response = await fetch(`${API_BASE}/chat/new`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        if (result.success) {
            currentChatId = result.chatId;
            await loadChatList();
            selectChat(result.chatId);

            // 清空聊天界面
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';

            // 更新标题
            document.getElementById('chatTitle').textContent = '新对话';

            return true;
        } else {
            showNotification('创建对话失败: ' + result.error, 'error');
            return false;
        }
    } catch (error) {
        console.error('创建新对话失败:', error);
        showNotification('创建对话失败，请检查网络连接', 'error');
        return false;
    }
}

// 加载聊天列表
async function loadChatList() {
    try {
        const response = await fetch(`${API_BASE}/chats`);
        const result = await response.json();
        
        if (result.success) {
            chats = result.chats;
            renderChatList();
        } else {
            console.error('加载聊天列表失败:', result.error);
        }
    } catch (error) {
        console.error('加载聊天列表失败:', error);
    }
}

// 渲染聊天列表
function renderChatList() {
    const chatList = document.getElementById('chatList');
    const emptyChatList = document.getElementById('emptyChatList');

    if (chats.length === 0) {
        chatList.innerHTML = '';
        if (emptyChatList) {
            emptyChatList.style.display = 'block';
        }
        return;
    }

    if (emptyChatList) {
        emptyChatList.style.display = 'none';
    }

    chatList.innerHTML = '';

    chats.forEach(chat => {
        const chatItem = document.createElement('div');
        chatItem.className = `chat-item ${currentChatId === chat.chatId ? 'active' : ''}`;

        // 生成简短的预览文本
        const previewText = chat.title.length > 30 ? chat.title.substring(0, 30) + '...' : chat.title;
        const timeAgo = getTimeAgo(new Date(chat.createdAt));

        chatItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start" onclick="selectChat('${chat.chatId}')">
                <div class="flex-grow-1">
                    <div class="fw-semibold mb-1">${previewText}</div>
                    <div class="small text-muted d-flex align-items-center">
                        <i class="bi bi-clock me-1"></i>
                        ${timeAgo}
                        <span class="ms-2">
                            <i class="bi bi-chat-dots me-1"></i>
                            ${chat.totalRounds}
                        </span>
                    </div>
                </div>
                <div class="dropdown" onclick="event.stopPropagation()">
                    <button class="btn btn-sm btn-link text-muted p-1" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="editChatTitle('${chat.chatId}', '${chat.title.replace(/'/g, "\\'")}')">
                            <i class="bi bi-pencil me-2"></i>重命名
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteChat('${chat.chatId}')">
                            <i class="bi bi-trash me-2"></i>删除
                        </a></li>
                    </ul>
                </div>
            </div>
        `;

        chatList.appendChild(chatItem);
    });
}

// 选择聊天
async function selectChat(chatId) {
    currentChatId = chatId;
    const chat = chats.find(c => c.chatId === chatId);
    
    if (chat) {
        document.getElementById('chatTitle').textContent = chat.title;
        await loadChatHistory(chatId);
        renderChatList(); // 重新渲染以更新选中状态
        
        // 如果上下文面板是打开的，加载轮次列表
        if (document.getElementById('contextPanel').style.display !== 'none') {
            loadRoundsList();
        }
    }
}

// 加载聊天历史
async function loadChatHistory(chatId) {
    try {
        const response = await fetch(`${API_BASE}/chat/${chatId}/history`);
        const result = await response.json();
        
        if (result.success) {
            renderChatMessages(result.history);
        } else {
            console.error('加载聊天历史失败:', result.error);
        }
    } catch (error) {
        console.error('加载聊天历史失败:', error);
    }
}

// 渲染聊天消息
function renderChatMessages(history) {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = '';
    
    if (history.length === 0) {
        chatMessages.innerHTML = `
            <div class="text-center text-muted">
                <i class="bi bi-chat-dots" style="font-size: 3rem;"></i>
                <p>还没有消息，开始对话吧！</p>
            </div>
        `;
        return;
    }
    
    history.forEach(round => {
        // 兼容新旧格式
        if (round.messages) {
            // 新格式：使用messages数组
            round.messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${message.role === 'user' ? 'user' : 'assistant'}`;

                if (message.role === 'user') {
                    messageDiv.innerHTML = `
                        <div class="mb-1">${escapeHtml(message.content)}</div>
                        <small class="opacity-75">${message.user_name || '用户'} - ${new Date(message.timestamp).toLocaleString()}</small>
                    `;
                } else {
                    const modelInfo = message.model ? ` (${message.model})` : '';
                    const durationInfo = message.duration ? ` - ${message.duration.toFixed(2)}s` : '';
                    messageDiv.innerHTML = `
                        <div class="mb-1">${escapeHtml(message.content)}</div>
                        <small class="text-muted">AI${modelInfo}${durationInfo} - ${new Date(message.timestamp).toLocaleString()}</small>
                    `;
                }
                chatMessages.appendChild(messageDiv);
            });
        } else {
            // 旧格式：使用userMessage和aiResponse
            const userMessage = document.createElement('div');
            userMessage.className = 'message user';
            userMessage.innerHTML = `
                <div class="mb-1">${escapeHtml(round.userMessage)}</div>
                <small class="opacity-75">第${round.roundNumber}轮 - ${new Date(round.timestamp).toLocaleString()}</small>
            `;
            chatMessages.appendChild(userMessage);

            const aiMessage = document.createElement('div');
            aiMessage.className = 'message assistant';
            const modelInfo = round.model ? ` (${round.model})` : '';
            aiMessage.innerHTML = `
                <div class="mb-1">${escapeHtml(round.aiResponse)}</div>
                <small class="text-muted">${round.apiProvider}${modelInfo} - ${new Date(round.timestamp).toLocaleString()}</small>
            `;
            chatMessages.appendChild(aiMessage);
        }
    });
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 发送消息
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) {
        return;
    }

    // 如果是第一条消息且没有当前对话，自动创建新对话
    if (!currentChatId) {
        await createNewChat();
        if (!currentChatId) {
            showNotification('创建对话失败，请重试', 'error');
            return;
        }
    }

    // 隐藏欢迎消息
    const welcomeMessage = document.getElementById('welcomeMessage');
    if (welcomeMessage) {
        welcomeMessage.style.display = 'none';
    }

    // 显示高级功能按钮
    if (isFirstMessage) {
        document.getElementById('modelDropdown').style.display = 'block';
        document.getElementById('advancedBtn').style.display = 'block';
        isFirstMessage = false;
    }

    // 智能选择模型
    const { apiProvider, selectedModel } = getSmartModel(message);

    // 显示用户消息
    addMessageToChat('user', message);
    messageInput.value = '';

    // 显示输入状态
    showTypingIndicator();
    disableInput(true);

    try {
        const response = await fetch(`${API_BASE}/chat/${currentChatId}/message`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                apiProvider: apiProvider,
                model: selectedModel
            })
        });

        const result = await response.json();

        if (result.success) {
            // 显示AI回复
            addMessageToChat('assistant', result.response);
            await loadChatList(); // 更新聊天列表

            // 如果上下文面板是打开的，更新轮次列表
            if (document.getElementById('contextPanel').style.display !== 'none') {
                loadRoundsList();
            }
        } else {
            showNotification('发送失败: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('发送消息失败:', error);
        showNotification('网络错误，请检查连接后重试', 'error');
    } finally {
        hideTypingIndicator();
        disableInput(false);
        messageInput.focus();
    }
}

// 显示上下文管理面板
function showContextManager() {
    if (!currentChatId) {
        alert('请先选择一个对话');
        return;
    }
    
    document.getElementById('contextPanel').style.display = 'block';
    document.querySelector('.col-md-6').className = 'col-md-4 chat-main p-0';
    loadRoundsList();
}

// 隐藏上下文管理面板
function hideContextManager() {
    document.getElementById('contextPanel').style.display = 'none';
    document.querySelector('.col-md-4').className = 'col-md-6 chat-main p-0';
}

// 加载轮次列表
async function loadRoundsList() {
    if (!currentChatId) return;
    
    try {
        const response = await fetch(`${API_BASE}/chat/${currentChatId}/history`);
        const result = await response.json();
        
        if (result.success) {
            renderRoundsList(result.history);
        }
    } catch (error) {
        console.error('加载轮次列表失败:', error);
    }
}

// 渲染轮次列表
function renderRoundsList(history) {
    const roundsList = document.getElementById('roundsList');
    roundsList.innerHTML = '';
    
    history.forEach((round, index) => {
        const roundItem = document.createElement('div');
        roundItem.className = 'round-item p-2 border-bottom';

        // 兼容新旧格式获取用户消息和时间戳
        let userMessage, timestamp;
        if (round.messages) {
            // 新格式
            const userMsg = round.messages.find(msg => msg.role === 'user');
            userMessage = userMsg ? userMsg.content : '无用户消息';
            timestamp = userMsg ? userMsg.timestamp : new Date().toISOString();
        } else {
            // 旧格式
            userMessage = round.userMessage || '无用户消息';
            timestamp = round.timestamp || new Date().toISOString();
        }

        const roundNumber = round.roundNumber || (index + 1);

        roundItem.innerHTML = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${roundNumber}"
                       onchange="toggleRoundSelection(${roundNumber})">
                <label class="form-check-label" onclick="selectRound(${roundNumber})">
                    <strong>第${roundNumber}轮</strong><br>
                    <small class="text-muted">${userMessage.substring(0, 30)}...</small><br>
                    <small class="text-muted">${new Date(timestamp).toLocaleString()}</small>
                </label>
            </div>
        `;

        roundsList.appendChild(roundItem);
    });
}

// 切换轮次选择状态
function toggleRoundSelection(roundNumber) {
    if (selectedRounds.has(roundNumber)) {
        selectedRounds.delete(roundNumber);
    } else {
        selectedRounds.add(roundNumber);
    }
}

// 选择轮次查看JSON
async function selectRound(roundNumber) {
    currentRoundNumber = roundNumber;
    
    // 更新选中状态
    document.querySelectorAll('.round-item').forEach(item => {
        item.classList.remove('selected');
    });
    event.target.closest('.round-item').classList.add('selected');
    
    await loadRoundJson(roundNumber);
}

// 加载轮次JSON数据
async function loadRoundJson(roundNumber) {
    try {
        const response = await fetch(`${API_BASE}/chat/${currentChatId}/round/${roundNumber}`);
        const result = await response.json();
        
        if (result.success) {
            document.getElementById('jsonEditor').value = JSON.stringify(result.data, null, 2);
            renderContextItems(result.data.context || []);
        }
    } catch (error) {
        console.error('加载轮次JSON失败:', error);
    }
}

// 渲染上下文项目
function renderContextItems(context) {
    const contextItems = document.getElementById('contextItems');
    contextItems.innerHTML = '';
    
    context.forEach((item, index) => {
        const contextItem = document.createElement('div');
        contextItem.className = 'context-item';
        contextItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <strong>${item.role === 'user' ? '用户' : 'AI'}:</strong>
                    <p class="mb-1">${escapeHtml(item.content.substring(0, 100))}${item.content.length > 100 ? '...' : ''}</p>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="removeContextItem(${index})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;
        
        contextItems.appendChild(contextItem);
    });
}

// 移除上下文项目
async function removeContextItem(index) {
    if (!currentRoundNumber) return;

    try {
        const jsonData = JSON.parse(document.getElementById('jsonEditor').value);
        if (jsonData.context && jsonData.context[index]) {
            jsonData.context.splice(index, 1);
            document.getElementById('jsonEditor').value = JSON.stringify(jsonData, null, 2);
            renderContextItems(jsonData.context);
        }
    } catch (error) {
        console.error('移除上下文项目失败:', error);
        alert('移除上下文项目失败: ' + error.message);
    }
}

// 保存JSON修改
async function saveJsonChanges() {
    if (!currentChatId || !currentRoundNumber) {
        alert('请先选择一个轮次');
        return;
    }

    try {
        const jsonData = JSON.parse(document.getElementById('jsonEditor').value);

        const response = await fetch(`${API_BASE}/chat/${currentChatId}/round/${currentRoundNumber}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ data: jsonData })
        });

        const result = await response.json();

        if (result.success) {
            alert('保存成功！');
            await loadChatHistory(currentChatId); // 重新加载聊天历史
        } else {
            alert('保存失败: ' + result.error);
        }
    } catch (error) {
        console.error('保存JSON修改失败:', error);
        alert('保存失败: ' + error.message);
    }
}

// 重新加载JSON数据
async function reloadJsonData() {
    if (currentRoundNumber) {
        await loadRoundJson(currentRoundNumber);
        alert('数据已重新加载');
    }
}

// 批量删除轮次
async function batchDeleteRounds() {
    if (selectedRounds.size === 0) {
        alert('请先选择要删除的轮次');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedRounds.size} 轮对话吗？此操作不可撤销。`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/chat/${currentChatId}/batch-delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ roundNumbers: Array.from(selectedRounds) })
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            selectedRounds.clear();
            await loadChatHistory(currentChatId);
            await loadChatList();
            loadRoundsList();

            // 清空JSON编辑器
            document.getElementById('jsonEditor').value = '';
            document.getElementById('contextItems').innerHTML = '';
            currentRoundNumber = null;
        } else {
            alert('批量删除失败: ' + result.error);
        }
    } catch (error) {
        console.error('批量删除失败:', error);
        alert('批量删除失败: ' + error.message);
    }
}

// 编辑聊天标题
function editChatTitle(chatId, currentTitle) {
    document.getElementById('newTitleInput').value = currentTitle;
    document.getElementById('newTitleInput').dataset.chatId = chatId;

    const modal = new bootstrap.Modal(document.getElementById('editTitleModal'));
    modal.show();
}

// 保存新标题
async function saveNewTitle() {
    const newTitle = document.getElementById('newTitleInput').value.trim();
    const chatId = document.getElementById('newTitleInput').dataset.chatId;

    if (!newTitle) {
        alert('标题不能为空');
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/chat/${chatId}/title`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ title: newTitle })
        });

        const result = await response.json();

        if (result.success) {
            await loadChatList();
            if (currentChatId === chatId) {
                document.getElementById('chatTitle').textContent = newTitle;
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('editTitleModal'));
            modal.hide();
        } else {
            alert('更新标题失败: ' + result.error);
        }
    } catch (error) {
        console.error('更新标题失败:', error);
        alert('更新标题失败: ' + error.message);
    }
}

// 删除聊天
async function deleteChat(chatId) {
    const chat = chats.find(c => c.chatId === chatId);
    if (!chat) return;

    if (!confirm(`确定要删除对话"${chat.title}"吗？此操作不可撤销。`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/chat/${chatId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            await loadChatList();

            // 如果删除的是当前选中的聊天，清空界面
            if (currentChatId === chatId) {
                currentChatId = null;
                currentRoundNumber = null;
                selectedRounds.clear();

                document.getElementById('chatTitle').textContent = '选择或创建一个对话';
                document.getElementById('chatMessages').innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-chat-dots" style="font-size: 3rem;"></i>
                        <p>开始新的对话吧！</p>
                    </div>
                `;

                // 清空上下文管理面板
                document.getElementById('roundsList').innerHTML = '';
                document.getElementById('jsonEditor').value = '';
                document.getElementById('contextItems').innerHTML = '';
            }

            alert('对话删除成功');
        } else {
            alert('删除对话失败: ' + result.error);
        }
    } catch (error) {
        console.error('删除对话失败:', error);
        alert('删除对话失败: ' + error.message);
    }
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
