const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

const app = express();
const PORT = 8080;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 确保聊天数据目录存在
const CHATS_DIR = path.join(__dirname, 'chats');
fs.ensureDirSync(CHATS_DIR);

// API配置
const API_CONFIGS = {
  deepseek: {
    url: 'https://api.deepseek.com/v1/chat/completions',
    key: '***********************************',
    models: {
      'deepseek-chat': 'DeepSeek Chat (V3-0324)',
      'deepseek-reasoner': 'DeepSeek R1 (推理模型)'
    }
  },
  gemini: {
    keys: [
      'AIzaSyA_7e5i1X0Qwqt64GM0YxAu4AuUBXlM2gU',
      'AIzaSyDL-n-QZrMGnBdZPuuX7XMJ5htg-jEA8Bc'
    ],
    models: {
      'gemini-2.0-flash': {
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
        name: 'Gemini 2.0 Flash (快速)'
      },
      'gemini-2.5-pro': {
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent',
        name: 'Gemini 2.5 Pro (专业)'
      },
      'gemini-2.5-flash': {
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent',
        name: 'Gemini 2.5 Flash (最新快速)'
      }
    }
  }
};

// 创建新的聊天会话
app.post('/api/chat/new', async (req, res) => {
  try {
    const chatId = uuidv4();
    const chatDir = path.join(CHATS_DIR, chatId);
    
    // 创建聊天目录
    await fs.ensureDir(chatDir);
    
    // 创建初始元数据
    const metadata = {
      chatId,
      createdAt: new Date().toISOString(),
      title: '新对话',
      totalRounds: 0,
      apiProvider: 'deepseek'
    };
    
    await fs.writeJson(path.join(chatDir, 'metadata.json'), metadata, { spaces: 2 });
    
    res.json({ success: true, chatId, metadata });
  } catch (error) {
    console.error('创建新聊天失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取可用的AI模型列表
app.get('/api/models', (req, res) => {
  try {
    const models = {
      deepseek: API_CONFIGS.deepseek.models,
      gemini: {}
    };

    // 转换Gemini模型格式
    for (const [key, value] of Object.entries(API_CONFIGS.gemini.models)) {
      models.gemini[key] = value.name;
    }

    res.json({ success: true, models });
  } catch (error) {
    console.error('获取模型列表失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取所有聊天列表
app.get('/api/chats', async (req, res) => {
  try {
    const chatDirs = await fs.readdir(CHATS_DIR);
    const chats = [];
    
    for (const chatDir of chatDirs) {
      const chatPath = path.join(CHATS_DIR, chatDir);
      const metadataPath = path.join(chatPath, 'metadata.json');
      
      if (await fs.pathExists(metadataPath)) {
        const metadata = await fs.readJson(metadataPath);
        chats.push(metadata);
      }
    }
    
    // 按创建时间排序
    chats.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    res.json({ success: true, chats });
  } catch (error) {
    console.error('获取聊天列表失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 发送消息
app.post('/api/chat/:chatId/message', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { message, apiProvider = 'deepseek', model } = req.body;
    
    const chatDir = path.join(CHATS_DIR, chatId);
    const metadataPath = path.join(chatDir, 'metadata.json');
    
    if (!await fs.pathExists(metadataPath)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }
    
    const metadata = await fs.readJson(metadataPath);
    const roundNumber = metadata.totalRounds + 1;
    
    // 获取历史上下文
    const context = await getContextHistory(chatDir, roundNumber - 1);
    
    // 记录开始时间
    const startTime = Date.now();

    // 调用AI API
    const aiResponse = await callAIAPI(apiProvider, message, context, model);

    // 计算响应时间
    const duration = (Date.now() - startTime) / 1000;

    // 保存这一轮的对话JSON - 使用新的格式
    const roundData = {
      messages: [
        {
          role: "user",
          content: message,
          timestamp: new Date().toISOString(),
          model: model || (apiProvider === 'deepseek' ? 'deepseek-chat' : 'gemini-2.0-flash'),
          user_name: "用户"
        },
        {
          role: "assistant",
          content: aiResponse,
          timestamp: new Date().toISOString(),
          model: model || (apiProvider === 'deepseek' ? 'deepseek-chat' : 'gemini-2.0-flash'),
          model_display_name: model || (apiProvider === 'deepseek' ? 'deepseek-chat' : 'gemini-2.0-flash'),
          duration: duration,
          response_length: aiResponse.length
        }
      ]
    };
    
    const roundFile = path.join(chatDir, `round_${roundNumber.toString().padStart(3, '0')}.json`);
    await fs.writeJson(roundFile, roundData, { spaces: 2 });
    
    // 更新元数据
    metadata.totalRounds = roundNumber;
    metadata.lastUpdated = new Date().toISOString();
    metadata.apiProvider = apiProvider;
    
    // 如果是第一轮，用用户消息的前30个字符作为标题
    if (roundNumber === 1) {
      metadata.title = message.substring(0, 30) + (message.length > 30 ? '...' : '');
    }
    
    await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    
    res.json({ 
      success: true, 
      response: aiResponse, 
      roundNumber,
      metadata 
    });
    
  } catch (error) {
    console.error('发送消息失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取聊天历史
app.get('/api/chat/:chatId/history', async (req, res) => {
  try {
    const { chatId } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);
    
    if (!await fs.pathExists(chatDir)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }
    
    const files = await fs.readdir(chatDir);
    const roundFiles = files.filter(f => f.startsWith('round_') && f.endsWith('.json'));
    
    const history = [];
    for (const file of roundFiles.sort()) {
      const roundData = await fs.readJson(path.join(chatDir, file));
      history.push(roundData);
    }
    
    res.json({ success: true, history });
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取上下文历史（用于AI调用）
async function getContextHistory(chatDir, maxRounds = 10) {
  try {
    const files = await fs.readdir(chatDir);
    const roundFiles = files
      .filter(f => f.startsWith('round_') && f.endsWith('.json'))
      .sort()
      .slice(-maxRounds); // 只取最近的几轮
    
    const context = [];
    for (const file of roundFiles) {
      const roundData = await fs.readJson(path.join(chatDir, file));

      // 兼容新旧格式
      if (roundData.messages) {
        // 新格式：直接使用messages数组
        for (const message of roundData.messages) {
          context.push({
            role: message.role,
            content: message.content
          });
        }
      } else {
        // 旧格式：使用userMessage和aiResponse
        context.push(
          { role: 'user', content: roundData.userMessage },
          { role: 'assistant', content: roundData.aiResponse }
        );
      }
    }
    
    return context;
  } catch (error) {
    console.error('获取上下文历史失败:', error);
    return [];
  }
}

// 调用AI API
async function callAIAPI(provider, message, context = [], model) {
  try {
    if (provider === 'deepseek') {
      // 如果没有指定模型，使用默认模型
      const deepseekModel = model || 'deepseek-chat';
      return await callDeepSeekAPI(message, context, deepseekModel);
    } else if (provider === 'gemini') {
      // 如果没有指定模型，使用默认模型
      const geminiModel = model || 'gemini-2.0-flash';
      return await callGeminiAPI(message, context, geminiModel);
    } else {
      throw new Error('不支持的API提供商');
    }
  } catch (error) {
    console.error(`调用${provider} API失败:`, error.message);

    // 如果是Gemini API失败，提供更友好的错误信息
    if (provider === 'gemini') {
      if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
        throw new Error('Gemini API连接超时，请检查网络连接或稍后重试');
      } else if (error.response?.status === 403) {
        throw new Error('Gemini API密钥无效或已过期');
      } else if (error.response?.status === 429) {
        throw new Error('Gemini API调用频率超限，请稍后重试');
      } else {
        throw new Error(`Gemini API调用失败: ${error.message}`);
      }
    }

    throw error;
  }
}

// DeepSeek API调用
async function callDeepSeekAPI(message, context, model = 'deepseek-chat') {
  const messages = [
    ...context,
    { role: 'user', content: message }
  ];

  console.log(`调用DeepSeek API (${model})，消息数量:`, messages.length);

  const response = await axios.post(API_CONFIGS.deepseek.url, {
    model: model,
    messages: messages,
    temperature: 0.7,
    max_tokens: 4000
  }, {
    headers: {
      'Authorization': `Bearer ${API_CONFIGS.deepseek.key}`,
      'Content-Type': 'application/json'
    },
    timeout: 30000 // 30秒超时
  });

  console.log(`DeepSeek API (${model}) 调用成功`);
  return response.data.choices[0].message.content;
}

// Gemini API调用
async function callGeminiAPI(message, context, model = 'gemini-2.0-flash') {
  // 构建Gemini格式的内容
  const contents = [];

  // 添加历史上下文
  for (const msg of context) {
    contents.push({
      parts: [{ text: msg.content }],
      role: msg.role === 'assistant' ? 'model' : 'user'
    });
  }

  // 添加当前消息
  contents.push({
    parts: [{ text: message }],
    role: 'user'
  });

  const modelConfig = API_CONFIGS.gemini.models[model];
  if (!modelConfig) {
    throw new Error(`不支持的Gemini模型: ${model}`);
  }

  console.log(`调用Gemini API (${model})，消息数量:`, contents.length);

  // 尝试使用不同的API密钥
  for (let i = 0; i < API_CONFIGS.gemini.keys.length; i++) {
    const apiKey = API_CONFIGS.gemini.keys[i];
    try {
      console.log(`尝试使用Gemini API密钥 ${i + 1}/${API_CONFIGS.gemini.keys.length}`);

      const response = await axios.post(modelConfig.url, {
        contents: contents,
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 4000
        }
      }, {
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': apiKey
        },
        timeout: 30000 // 30秒超时
      });

      console.log(`Gemini API (${model}) 调用成功`);
      return response.data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.warn(`Gemini API密钥 ${i + 1} 调用失败:`, error.response?.data || error.message);
      if (i === API_CONFIGS.gemini.keys.length - 1) {
        throw new Error(`所有Gemini API密钥都调用失败。最后错误: ${error.response?.data?.error?.message || error.message}`);
      }
      continue;
    }
  }
}

// 获取特定轮次的JSON数据
app.get('/api/chat/:chatId/round/:roundNumber', async (req, res) => {
  try {
    const { chatId, roundNumber } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);
    const roundFile = path.join(chatDir, `round_${roundNumber.padStart(3, '0')}.json`);

    if (!await fs.pathExists(roundFile)) {
      return res.status(404).json({ success: false, error: '轮次不存在' });
    }

    const roundData = await fs.readJson(roundFile);
    res.json({ success: true, data: roundData });
  } catch (error) {
    console.error('获取轮次数据失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 更新特定轮次的JSON数据
app.put('/api/chat/:chatId/round/:roundNumber', async (req, res) => {
  try {
    const { chatId, roundNumber } = req.params;
    const { data } = req.body;

    const chatDir = path.join(CHATS_DIR, chatId);
    const roundFile = path.join(chatDir, `round_${roundNumber.padStart(3, '0')}.json`);

    if (!await fs.pathExists(roundFile)) {
      return res.status(404).json({ success: false, error: '轮次不存在' });
    }

    // 添加更新时间戳
    data.lastModified = new Date().toISOString();

    await fs.writeJson(roundFile, data, { spaces: 2 });
    res.json({ success: true, message: '更新成功' });
  } catch (error) {
    console.error('更新轮次数据失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 删除特定轮次
app.delete('/api/chat/:chatId/round/:roundNumber', async (req, res) => {
  try {
    const { chatId, roundNumber } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);
    const roundFile = path.join(chatDir, `round_${roundNumber.padStart(3, '0')}.json`);

    if (!await fs.pathExists(roundFile)) {
      return res.status(404).json({ success: false, error: '轮次不存在' });
    }

    await fs.remove(roundFile);

    // 更新元数据中的总轮数
    const metadataPath = path.join(chatDir, 'metadata.json');
    if (await fs.pathExists(metadataPath)) {
      const metadata = await fs.readJson(metadataPath);
      const files = await fs.readdir(chatDir);
      const remainingRounds = files.filter(f => f.startsWith('round_') && f.endsWith('.json')).length;
      metadata.totalRounds = remainingRounds;
      metadata.lastUpdated = new Date().toISOString();
      await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    }

    res.json({ success: true, message: '删除成功' });
  } catch (error) {
    console.error('删除轮次失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 删除整个聊天
app.delete('/api/chat/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const chatDir = path.join(CHATS_DIR, chatId);

    if (!await fs.pathExists(chatDir)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }

    await fs.remove(chatDir);
    res.json({ success: true, message: '聊天删除成功' });
  } catch (error) {
    console.error('删除聊天失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 批量删除轮次
app.post('/api/chat/:chatId/batch-delete', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { roundNumbers } = req.body;

    const chatDir = path.join(CHATS_DIR, chatId);
    const deletedRounds = [];

    for (const roundNumber of roundNumbers) {
      const roundFile = path.join(chatDir, `round_${roundNumber.toString().padStart(3, '0')}.json`);
      if (await fs.pathExists(roundFile)) {
        await fs.remove(roundFile);
        deletedRounds.push(roundNumber);
      }
    }

    // 更新元数据
    const metadataPath = path.join(chatDir, 'metadata.json');
    if (await fs.pathExists(metadataPath)) {
      const metadata = await fs.readJson(metadataPath);
      const files = await fs.readdir(chatDir);
      const remainingRounds = files.filter(f => f.startsWith('round_') && f.endsWith('.json')).length;
      metadata.totalRounds = remainingRounds;
      metadata.lastUpdated = new Date().toISOString();
      await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    }

    res.json({ success: true, deletedRounds, message: `成功删除 ${deletedRounds.length} 轮对话` });
  } catch (error) {
    console.error('批量删除失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 更新聊天标题
app.put('/api/chat/:chatId/title', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { title } = req.body;

    const chatDir = path.join(CHATS_DIR, chatId);
    const metadataPath = path.join(chatDir, 'metadata.json');

    if (!await fs.pathExists(metadataPath)) {
      return res.status(404).json({ success: false, error: '聊天不存在' });
    }

    const metadata = await fs.readJson(metadataPath);
    metadata.title = title;
    metadata.lastUpdated = new Date().toISOString();

    await fs.writeJson(metadataPath, metadata, { spaces: 2 });
    res.json({ success: true, message: '标题更新成功' });
  } catch (error) {
    console.error('更新标题失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({ success: false, error: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ success: false, error: '接口不存在' });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📁 聊天数据目录: ${CHATS_DIR}`);
  console.log(`🔧 API配置:`);
  console.log(`   - DeepSeek: ${API_CONFIGS.deepseek.url}`);
  console.log(`   - Gemini: ${API_CONFIGS.gemini.url}`);
});
