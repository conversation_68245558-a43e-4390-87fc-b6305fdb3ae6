// 测试真实API功能的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testRealAPI() {
    try {
        console.log('🚀 开始测试真实AI API功能...\n');
        
        // 1. 创建新对话
        console.log('1. 创建新对话...');
        const newChatResponse = await axios.post(`${BASE_URL}/chat/new`);
        console.log('✅ 新对话创建成功:', newChatResponse.data);
        const chatId = newChatResponse.data.chatId;
        
        // 2. 测试DeepSeek API
        console.log('\n2. 测试DeepSeek API...');
        try {
            const deepseekResponse = await axios.post(`${BASE_URL}/chat/${chatId}/message`, {
                message: '你好，请简单介绍一下你自己，用中文回复',
                apiProvider: 'deepseek'
            });
            console.log('✅ DeepSeek API调用成功');
            console.log('回复内容:', deepseekResponse.data.response.substring(0, 100) + '...');
        } catch (error) {
            console.error('❌ DeepSeek API调用失败:', error.response?.data || error.message);
        }
        
        // 3. 创建另一个对话测试Gemini
        console.log('\n3. 创建新对话测试Gemini...');
        const newChatResponse2 = await axios.post(`${BASE_URL}/chat/new`);
        const chatId2 = newChatResponse2.data.chatId;
        
        try {
            const geminiResponse = await axios.post(`${BASE_URL}/chat/${chatId2}/message`, {
                message: '你好，请简单介绍一下你自己，用中文回复',
                apiProvider: 'gemini'
            });
            console.log('✅ Gemini API调用成功');
            console.log('回复内容:', geminiResponse.data.response.substring(0, 100) + '...');
        } catch (error) {
            console.error('❌ Gemini API调用失败:', error.response?.data || error.message);
        }
        
        // 4. 测试上下文功能
        console.log('\n4. 测试上下文功能（第二轮对话）...');
        try {
            const contextResponse = await axios.post(`${BASE_URL}/chat/${chatId}/message`, {
                message: '请记住我刚才问了什么问题，并简要回顾一下',
                apiProvider: 'deepseek'
            });
            console.log('✅ 上下文测试成功');
            console.log('回复内容:', contextResponse.data.response.substring(0, 100) + '...');
        } catch (error) {
            console.error('❌ 上下文测试失败:', error.response?.data || error.message);
        }
        
        // 5. 获取聊天历史
        console.log('\n5. 获取聊天历史...');
        const historyResponse = await axios.get(`${BASE_URL}/chat/${chatId}/history`);
        console.log('✅ 聊天历史获取成功，共', historyResponse.data.history.length, '轮对话');
        
        // 6. 获取JSON数据
        console.log('\n6. 获取第一轮JSON数据...');
        const roundResponse = await axios.get(`${BASE_URL}/chat/${chatId}/round/1`);
        console.log('✅ JSON数据获取成功');
        console.log('上下文项目数量:', roundResponse.data.data.context.length);
        
        console.log('\n🎉 真实API测试完成！');
        
    } catch (error) {
        console.error('❌ API测试失败:', error.response?.data || error.message);
    }
}

// 运行测试
testRealAPI();
