<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天上下文管理器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .chat-sidebar {
            height: 100vh;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .chat-main {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        .message.user {
            background-color: #007bff;
            color: white;
            margin-left: auto;
        }
        .message.assistant {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .chat-input {
            border-top: 1px solid #dee2e6;
            padding: 20px;
        }
        .json-editor {
            height: 400px;
            overflow-y: auto;
        }
        .round-item {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .round-item:hover {
            background-color: #f8f9fa;
        }
        .round-item.selected {
            background-color: #e3f2fd;
        }
        .context-item {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin-bottom: 10px;
            padding: 10px;
        }
        .context-item.selected {
            border-color: #007bff;
            background-color: #f0f8ff;
        }

        /* 新增样式 */
        .welcome-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .welcome-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .chat-input textarea:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
        }

        .chat-item {
            transition: background-color 0.2s;
            border-radius: 8px;
            margin-bottom: 8px;
            padding: 12px;
            cursor: pointer;
        }

        .chat-item:hover {
            background-color: #f8f9fa;
        }

        .chat-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #007bff;
        }

        @keyframes typing {
            0%, 60%, 100% { opacity: 0; }
            30% { opacity: 1; }
        }

        .typing-dots {
            animation: typing 1.4s infinite;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .chat-sidebar {
                position: fixed;
                left: -100%;
                top: 0;
                width: 80%;
                height: 100vh;
                background: white;
                z-index: 1040;
                transition: left 0.3s ease;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .chat-sidebar.show {
                left: 0;
            }

            .chat-main {
                width: 100%;
                margin-left: 0;
            }

            .mobile-header {
                display: flex;
                justify-content: between;
                align-items: center;
                padding: 10px 15px;
                border-bottom: 1px solid #dee2e6;
                background: white;
                position: sticky;
                top: 0;
                z-index: 1030;
            }

            .mobile-menu-btn {
                background: none;
                border: none;
                font-size: 1.2rem;
                color: #007bff;
            }

            .chat-messages {
                padding: 15px;
                height: calc(100vh - 200px);
            }

            .chat-input {
                padding: 15px;
                background: white;
                border-top: 1px solid #dee2e6;
                position: sticky;
                bottom: 0;
            }

            .welcome-message .row {
                margin: 0;
            }

            .welcome-message .col-md-6 {
                padding: 5px;
            }

            .welcome-message .card {
                margin-bottom: 10px;
            }

            #contextPanel {
                position: fixed;
                right: -100%;
                top: 0;
                width: 90%;
                height: 100vh;
                background: white;
                z-index: 1050;
                transition: right 0.3s ease;
                box-shadow: -2px 0 10px rgba(0,0,0,0.1);
                overflow-y: auto;
            }

            #contextPanel.show {
                right: 0;
            }
        }

        /* 遮罩层 */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1035;
            display: none;
        }
    </style>
</head>
<body>
    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" onclick="closeMobileMenus()"></div>

    <div class="container-fluid">
        <!-- 移动端顶部导航 -->
        <div class="mobile-header d-md-none">
            <button class="mobile-menu-btn" onclick="toggleMobileSidebar()">
                <i class="bi bi-list"></i>
            </button>
            <h6 class="mb-0 flex-grow-1 text-center" id="mobileTitle">AI 智能助手</h6>
            <button class="mobile-menu-btn" onclick="toggleMobileSettings()">
                <i class="bi bi-sliders"></i>
            </button>
        </div>

        <div class="row">
            <!-- 左侧边栏 - 聊天历史 -->
            <div class="col-md-3 chat-sidebar p-0">
                <div class="p-3 border-bottom">
                    <button class="btn btn-primary w-100 rounded-pill" onclick="createNewChat()">
                        <i class="bi bi-plus-lg"></i> 新对话
                    </button>
                </div>
                <div class="p-3">
                    <h6 class="text-muted mb-3">
                        <i class="bi bi-clock-history"></i> 聊天历史
                    </h6>
                    <div id="chatList">
                        <!-- 聊天列表将在这里动态加载 -->
                        <div class="text-center text-muted py-4" id="emptyChatList">
                            <i class="bi bi-chat-left-dots" style="font-size: 2rem;"></i>
                            <p class="mt-2 small">还没有聊天记录<br>点击上方按钮开始新对话</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间 - 聊天界面 -->
            <div class="col-md-6 chat-main p-0">
                <div class="p-3 border-bottom d-flex justify-content-between align-items-center">
                    <h5 id="chatTitle" class="mb-0">AI 智能助手</h5>
                    <div class="d-flex align-items-center">
                        <!-- 简化的模型选择 -->
                        <div class="dropdown me-2" id="modelDropdown" style="display: none;">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <span id="currentModel">智能模式</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="switchModel('auto')">🤖 智能模式 (推荐)</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="switchModel('deepseek-chat')">💬 对话模式</a></li>
                                <li><a class="dropdown-item" href="#" onclick="switchModel('deepseek-reasoner')">🧠 推理模式</a></li>
                                <li><a class="dropdown-item" href="#" onclick="switchModel('gemini-2.0-flash')">⚡ 快速模式</a></li>
                            </ul>
                        </div>
                        <!-- 高级设置按钮 -->
                        <button class="btn btn-outline-secondary btn-sm" onclick="toggleAdvancedSettings()" id="advancedBtn" style="display: none;">
                            <i class="bi bi-sliders"></i>
                        </button>
                    </div>
                </div>
                
                <div id="chatMessages" class="chat-messages">
                    <div class="text-center text-muted" id="welcomeMessage">
                        <div class="mb-4">
                            <i class="bi bi-robot" style="font-size: 4rem; color: #007bff;"></i>
                        </div>
                        <h4 class="mb-3">👋 欢迎使用 AI 智能助手</h4>
                        <p class="mb-4">我可以帮您解答问题、写作、编程、翻译等各种任务</p>
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-lightbulb text-warning" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">💡 创意写作</h6>
                                        <small class="text-muted">帮您写文章、故事、邮件</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-code-slash text-success" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">💻 编程助手</h6>
                                        <small class="text-muted">代码编写、调试、优化</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-translate text-info" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">🌍 语言翻译</h6>
                                        <small class="text-muted">多语言翻译和学习</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-question-circle text-primary" style="font-size: 2rem;"></i>
                                        <h6 class="mt-2">❓ 问题解答</h6>
                                        <small class="text-muted">各种知识问题解答</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h6 class="text-muted mb-3">🚀 快速开始，试试这些问题：</h6>
                            <div class="d-flex flex-wrap gap-2 justify-content-center">
                                <button class="btn btn-outline-primary btn-sm" onclick="setQuickMessage('你好，请介绍一下你自己')">
                                    👋 打个招呼
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="setQuickMessage('帮我写一封邮件')">
                                    ✉️ 写邮件
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="setQuickMessage('解释一下人工智能')">
                                    🤖 学习AI
                                </button>
                                <button class="btn btn-outline-warning btn-sm" onclick="setQuickMessage('写一个简单的Python程序')">
                                    💻 编程帮助
                                </button>
                            </div>
                        </div>
                        <p class="text-muted small mt-3">💬 在下方输入框中输入您的问题，按回车键发送</p>
                    </div>
                </div>
                
                <div class="chat-input">
                    <div class="input-group">
                        <textarea id="messageInput" class="form-control" placeholder="💬 有什么可以帮您的吗？试试问我任何问题..." rows="2" style="resize: none;"></textarea>
                        <button class="btn btn-primary" onclick="sendMessage()" id="sendBtn">
                            <i class="bi bi-send"></i>
                            <span class="d-none d-sm-inline ms-1">发送</span>
                        </button>
                    </div>
                    <div class="mt-2 d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            按 Enter 发送，Shift+Enter 换行
                        </small>
                        <div id="typingIndicator" class="text-muted small" style="display: none;">
                            <i class="bi bi-three-dots"></i> AI 正在思考...
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧 - 上下文管理 -->
            <div class="col-md-3 p-3" id="contextPanel" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">上下文管理</h6>
                    <button class="btn btn-sm btn-outline-secondary" onclick="hideContextManager()">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                
                <!-- 轮次列表 -->
                <div class="mb-3">
                    <h6>对话轮次</h6>
                    <div id="roundsList" class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                        <!-- 轮次列表 -->
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-danger" onclick="batchDeleteRounds()">
                            <i class="bi bi-trash"></i> 批量删除选中
                        </button>
                    </div>
                </div>
                
                <!-- JSON编辑器 -->
                <div class="mb-3">
                    <h6>JSON编辑器</h6>
                    <div class="json-editor border rounded">
                        <textarea id="jsonEditor" class="form-control h-100" style="border: none; resize: none;"></textarea>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-success" onclick="saveJsonChanges()">
                            <i class="bi bi-save"></i> 保存修改
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="reloadJsonData()">
                            <i class="bi bi-arrow-clockwise"></i> 重新加载
                        </button>
                    </div>
                </div>
                
                <!-- 上下文项目管理 -->
                <div class="mb-3">
                    <h6>上下文项目</h6>
                    <div id="contextItems" style="max-height: 300px; overflow-y: auto;">
                        <!-- 上下文项目列表 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 - 编辑聊天标题 -->
    <div class="modal fade" id="editTitleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑对话标题</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="text" id="newTitleInput" class="form-control" placeholder="输入新标题">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewTitle()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
