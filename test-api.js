// 测试API功能的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAPI() {
    try {
        console.log('🚀 开始测试API功能...\n');
        
        // 1. 创建新对话
        console.log('1. 创建新对话...');
        const newChatResponse = await axios.post(`${BASE_URL}/chat/new`);
        console.log('✅ 新对话创建成功:', newChatResponse.data);
        const chatId = newChatResponse.data.chatId;
        
        // 2. 获取聊天列表
        console.log('\n2. 获取聊天列表...');
        const chatsResponse = await axios.get(`${BASE_URL}/chats`);
        console.log('✅ 聊天列表获取成功:', chatsResponse.data);
        
        // 3. 发送测试消息（使用DeepSeek）
        console.log('\n3. 发送测试消息（DeepSeek）...');
        const messageResponse = await axios.post(`${BASE_URL}/chat/${chatId}/message`, {
            message: '你好，请简单介绍一下你自己',
            apiProvider: 'deepseek'
        });
        console.log('✅ 消息发送成功:', messageResponse.data);
        
        // 4. 获取聊天历史
        console.log('\n4. 获取聊天历史...');
        const historyResponse = await axios.get(`${BASE_URL}/chat/${chatId}/history`);
        console.log('✅ 聊天历史获取成功:', historyResponse.data);
        
        // 5. 获取第一轮JSON数据
        console.log('\n5. 获取第一轮JSON数据...');
        const roundResponse = await axios.get(`${BASE_URL}/chat/${chatId}/round/1`);
        console.log('✅ 轮次数据获取成功:', JSON.stringify(roundResponse.data, null, 2));
        
        // 6. 更新聊天标题
        console.log('\n6. 更新聊天标题...');
        const titleResponse = await axios.put(`${BASE_URL}/chat/${chatId}/title`, {
            title: 'API测试对话'
        });
        console.log('✅ 标题更新成功:', titleResponse.data);
        
        console.log('\n🎉 所有API测试完成！');
        
    } catch (error) {
        console.error('❌ API测试失败:', error.response?.data || error.message);
    }
}

// 运行测试
testAPI();
