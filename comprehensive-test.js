// 全面功能测试脚本
const axios = require('axios');
const fs = require('fs-extra');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api';
let testResults = [];

function logTest(testName, success, details = '') {
    const result = success ? '✅' : '❌';
    const message = `${result} ${testName}${details ? ': ' + details : ''}`;
    console.log(message);
    testResults.push({ testName, success, details, message });
}

async function comprehensiveTest() {
    console.log('🧪 开始全面功能测试...\n');
    
    try {
        // ==================== 基础连接测试 ====================
        console.log('📡 基础连接测试');
        
        // 1. 服务器连接测试
        try {
            await axios.get('http://localhost:3000');
            logTest('服务器连接', true);
        } catch (error) {
            logTest('服务器连接', false, error.message);
            return;
        }
        
        // ==================== 对话管理测试 ====================
        console.log('\n💬 对话管理测试');
        
        // 2. 创建新对话
        let chatId1, chatId2;
        try {
            const response = await axios.post(`${BASE_URL}/chat/new`);
            chatId1 = response.data.chatId;
            logTest('创建新对话', true, `ID: ${chatId1.substring(0, 8)}...`);
        } catch (error) {
            logTest('创建新对话', false, error.message);
            return;
        }
        
        // 3. 创建第二个对话（测试多对话管理）
        try {
            const response = await axios.post(`${BASE_URL}/chat/new`);
            chatId2 = response.data.chatId;
            logTest('创建第二个对话', true, `ID: ${chatId2.substring(0, 8)}...`);
        } catch (error) {
            logTest('创建第二个对话', false, error.message);
        }
        
        // 4. 获取对话列表
        try {
            const response = await axios.get(`${BASE_URL}/chats`);
            const chatCount = response.data.chats.length;
            logTest('获取对话列表', true, `共${chatCount}个对话`);
        } catch (error) {
            logTest('获取对话列表', false, error.message);
        }
        
        // ==================== AI对话测试 ====================
        console.log('\n🤖 AI对话测试');
        
        // 5. 发送第一条消息（DeepSeek）
        try {
            const response = await axios.post(`${BASE_URL}/chat/${chatId1}/message`, {
                message: '你好，请简单介绍一下你自己',
                apiProvider: 'deepseek'
            }, { timeout: 60000 });
            
            const replyLength = response.data.response.length;
            logTest('DeepSeek API调用', true, `回复长度: ${replyLength}字符`);
        } catch (error) {
            logTest('DeepSeek API调用', false, error.response?.data?.error || error.message);
        }
        
        // 6. 发送第二条消息（测试上下文）
        try {
            const response = await axios.post(`${BASE_URL}/chat/${chatId1}/message`, {
                message: '请记住我刚才问了什么，并简要回顾',
                apiProvider: 'deepseek'
            }, { timeout: 60000 });
            
            const replyLength = response.data.response.length;
            logTest('上下文传递测试', true, `回复长度: ${replyLength}字符`);
        } catch (error) {
            logTest('上下文传递测试', false, error.response?.data?.error || error.message);
        }
        
        // 7. 发送第三条消息
        try {
            const response = await axios.post(`${BASE_URL}/chat/${chatId1}/message`, {
                message: '现在请告诉我今天的日期',
                apiProvider: 'deepseek'
            }, { timeout: 60000 });
            
            logTest('第三轮对话', true, '对话继续正常');
        } catch (error) {
            logTest('第三轮对话', false, error.response?.data?.error || error.message);
        }
        
        // 8. 测试Gemini API（预期可能失败）
        try {
            const response = await axios.post(`${BASE_URL}/chat/${chatId2}/message`, {
                message: '你好',
                apiProvider: 'gemini'
            }, { timeout: 30000 });
            
            logTest('Gemini API调用', true, '意外成功');
        } catch (error) {
            logTest('Gemini API调用', false, '预期的网络超时（正常）');
        }
        
        // ==================== JSON文件管理测试 ====================
        console.log('\n📄 JSON文件管理测试');
        
        // 9. 检查文件夹创建
        const chatDir = path.join(__dirname, 'chats', chatId1);
        try {
            const exists = await fs.pathExists(chatDir);
            logTest('对话文件夹创建', exists, `路径: chats/${chatId1.substring(0, 8)}...`);
        } catch (error) {
            logTest('对话文件夹创建', false, error.message);
        }
        
        // 10. 检查元数据文件
        try {
            const metadataPath = path.join(chatDir, 'metadata.json');
            const metadata = await fs.readJson(metadataPath);
            logTest('元数据文件', true, `标题: "${metadata.title}", 轮数: ${metadata.totalRounds}`);
        } catch (error) {
            logTest('元数据文件', false, error.message);
        }
        
        // 11. 检查轮次JSON文件
        for (let i = 1; i <= 3; i++) {
            try {
                const roundPath = path.join(chatDir, `round_${i.toString().padStart(3, '0')}.json`);
                const roundData = await fs.readJson(roundPath);
                const contextCount = roundData.context.length;
                logTest(`第${i}轮JSON文件`, true, `上下文项目: ${contextCount}个`);
            } catch (error) {
                logTest(`第${i}轮JSON文件`, false, '文件不存在或格式错误');
            }
        }
        
        // ==================== API接口测试 ====================
        console.log('\n🔌 API接口测试');
        
        // 12. 获取聊天历史
        try {
            const response = await axios.get(`${BASE_URL}/chat/${chatId1}/history`);
            const historyCount = response.data.history.length;
            logTest('获取聊天历史', true, `${historyCount}轮对话`);
        } catch (error) {
            logTest('获取聊天历史', false, error.message);
        }
        
        // 13. 获取特定轮次数据
        try {
            const response = await axios.get(`${BASE_URL}/chat/${chatId1}/round/1`);
            const contextLength = response.data.data.context.length;
            logTest('获取轮次数据', true, `第1轮上下文: ${contextLength}项`);
        } catch (error) {
            logTest('获取轮次数据', false, error.message);
        }
        
        // 14. 编辑JSON数据
        try {
            const getResponse = await axios.get(`${BASE_URL}/chat/${chatId1}/round/1`);
            const data = getResponse.data.data;
            data.testField = '测试编辑功能';
            data.editTime = new Date().toISOString();
            
            const updateResponse = await axios.put(`${BASE_URL}/chat/${chatId1}/round/1`, { data });
            logTest('编辑JSON数据', updateResponse.data.success, '添加测试字段');
        } catch (error) {
            logTest('编辑JSON数据', false, error.message);
        }
        
        // 15. 更新对话标题
        try {
            const response = await axios.put(`${BASE_URL}/chat/${chatId1}/title`, {
                title: '功能测试对话'
            });
            logTest('更新对话标题', response.data.success);
        } catch (error) {
            logTest('更新对话标题', false, error.message);
        }
        
        // ==================== 删除功能测试 ====================
        console.log('\n🗑️ 删除功能测试');
        
        // 16. 删除特定轮次
        try {
            const response = await axios.delete(`${BASE_URL}/chat/${chatId1}/round/2`);
            logTest('删除特定轮次', response.data.success, '删除第2轮');
        } catch (error) {
            logTest('删除特定轮次', false, error.message);
        }
        
        // 17. 批量删除轮次
        try {
            // 先创建一些新轮次用于测试
            await axios.post(`${BASE_URL}/chat/${chatId2}/message`, {
                message: '测试消息1',
                apiProvider: 'deepseek'
            }, { timeout: 30000 });
            
            await axios.post(`${BASE_URL}/chat/${chatId2}/message`, {
                message: '测试消息2',
                apiProvider: 'deepseek'
            }, { timeout: 30000 });
            
            // 批量删除
            const response = await axios.post(`${BASE_URL}/chat/${chatId2}/batch-delete`, {
                roundNumbers: [1, 2]
            });
            
            logTest('批量删除轮次', response.data.success, `删除${response.data.deletedRounds.length}轮`);
        } catch (error) {
            logTest('批量删除轮次', false, error.message);
        }
        
        // 18. 删除整个对话
        try {
            const response = await axios.delete(`${BASE_URL}/chat/${chatId2}`);
            logTest('删除整个对话', response.data.success);
        } catch (error) {
            logTest('删除整个对话', false, error.message);
        }
        
        // ==================== 错误处理测试 ====================
        console.log('\n⚠️ 错误处理测试');
        
        // 19. 访问不存在的对话
        try {
            await axios.get(`${BASE_URL}/chat/nonexistent/history`);
            logTest('不存在对话处理', false, '应该返回404错误');
        } catch (error) {
            const is404 = error.response?.status === 404;
            logTest('不存在对话处理', is404, '正确返回404错误');
        }
        
        // 20. 访问不存在的轮次
        try {
            await axios.get(`${BASE_URL}/chat/${chatId1}/round/999`);
            logTest('不存在轮次处理', false, '应该返回404错误');
        } catch (error) {
            const is404 = error.response?.status === 404;
            logTest('不存在轮次处理', is404, '正确返回404错误');
        }
        
    } catch (error) {
        console.error('❌ 测试过程中发生意外错误:', error.message);
    }
    
    // ==================== 测试总结 ====================
    console.log('\n📊 测试总结');
    console.log('='.repeat(50));
    
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${successRate}%`);
    
    if (failedTests > 0) {
        console.log('\n❌ 失败的测试:');
        testResults.filter(r => !r.success).forEach(r => {
            console.log(`  - ${r.testName}: ${r.details}`);
        });
    }
    
    console.log('\n🎯 核心功能状态:');
    const coreFeatures = [
        '服务器连接',
        'DeepSeek API调用',
        '上下文传递测试',
        '对话文件夹创建',
        '元数据文件',
        '第1轮JSON文件',
        '获取聊天历史',
        '编辑JSON数据'
    ];
    
    coreFeatures.forEach(feature => {
        const result = testResults.find(r => r.testName === feature);
        if (result) {
            console.log(`  ${result.success ? '✅' : '❌'} ${feature}`);
        }
    });
    
    if (passedTests >= totalTests * 0.8) {
        console.log('\n🎉 系统整体功能正常，可以投入使用！');
    } else {
        console.log('\n⚠️ 系统存在一些问题，建议检查失败的功能。');
    }
}

// 运行测试
comprehensiveTest();
